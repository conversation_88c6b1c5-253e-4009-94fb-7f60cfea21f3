{"name": "frontend-masters-state-workshop", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "test": "vitest"}, "dependencies": {"@libsql/client": "^0.15.7", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@tanstack/react-query": "^5.79.0", "@xstate/store": "^3.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.43.1", "immer": "^10.1.1", "lucide-react": "^0.511.0", "next": "15.3.2", "nuqs": "^2.4.3", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0", "zod": "^3.25.45"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.5.0", "drizzle-kit": "^0.31.1", "eslint": "^9", "eslint-config-next": "15.3.2", "jsdom": "^26.1.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.2", "typescript": "^5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.4"}}