{"version": "6", "dialect": "sqlite", "id": "32268f2e-77c8-4e9b-b650-8eb556a10760", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"activities": {"name": "activities", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "itinerary_id": {"name": "itinerary_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "destination_id": {"name": "destination_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "start_time": {"name": "start_time", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "end_time": {"name": "end_time", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"activities_itinerary_id_itineraries_id_fk": {"name": "activities_itinerary_id_itineraries_id_fk", "tableFrom": "activities", "tableTo": "itineraries", "columnsFrom": ["itinerary_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "activities_destination_id_destinations_id_fk": {"name": "activities_destination_id_destinations_id_fk", "tableFrom": "activities", "tableTo": "destinations", "columnsFrom": ["destination_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "comments": {"name": "comments", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "itinerary_id": {"name": "itinerary_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"comments_itinerary_id_itineraries_id_fk": {"name": "comments_itinerary_id_itineraries_id_fk", "tableFrom": "comments", "tableTo": "itineraries", "columnsFrom": ["itinerary_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "destinations": {"name": "destinations", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "itinerary_id": {"name": "itinerary_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "arrival_date": {"name": "arrival_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "departure_date": {"name": "departure_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'draft'"}}, "indexes": {}, "foreignKeys": {"destinations_itinerary_id_itineraries_id_fk": {"name": "destinations_itinerary_id_itineraries_id_fk", "tableFrom": "destinations", "tableTo": "itineraries", "columnsFrom": ["itinerary_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "flight_bookings": {"name": "flight_bookings", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "destination_id": {"name": "destination_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "airline": {"name": "airline", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "departure_time": {"name": "departure_time", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "arrival_time": {"name": "arrival_time", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"flight_bookings_destination_id_destinations_id_fk": {"name": "flight_bookings_destination_id_destinations_id_fk", "tableFrom": "flight_bookings", "tableTo": "destinations", "columnsFrom": ["destination_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "hotel_bookings": {"name": "hotel_bookings", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "destination_id": {"name": "destination_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "check_in": {"name": "check_in", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "check_out": {"name": "check_out", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"hotel_bookings_destination_id_destinations_id_fk": {"name": "hotel_bookings_destination_id_destinations_id_fk", "tableFrom": "hotel_bookings", "tableTo": "destinations", "columnsFrom": ["destination_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "itineraries": {"name": "itineraries", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "people": {"name": "people", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "references": {"name": "references", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}